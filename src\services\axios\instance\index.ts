import {ACTION_CODE, AXIOS_CONFIGS} from "@src/constants";
import {useProfile} from "@src/hooks";
import {deepParse, env} from "@src/utils";
import axios, {AxiosResponse, InternalAxiosRequestConfig} from "axios";
import {sha256} from "js-sha256";
import Base64x from "jsrsasign";

export const axiosInstance = axios.create(AXIOS_CONFIGS);

const handleError = (error: {config: any; data: any; headers: any; request: any; status: number; statusText: ""}) => {
  // handle error
  return Promise.reject(error);
};

//có response từ server
const handleResponse = (response: {statusCode: number; data: any}) => {
  // handle response
  return deepParse(response);
};

axiosInstance.interceptors.request.use(
  async <T>(request: InternalAxiosRequestConfig<any>) => {
    const {profile} = useProfile.getState();

    if (request.data?.actionCode === ACTION_CODE.DANG_NHAP) {
      request.headers.set("eAction", ACTION_CODE.DANG_NHAP);
      request.headers.set("eAuthToken", "b185d8dca0bc81eb7d6855869638f84b");
      request.headers.set("eSignature", sha256(Base64x.utf8tob64u(JSON.stringify(request.data)) + "." + env.VITE_SECRET_KEY));
      request.headers.set("Content-Type", "application/json");
    } else if (profile?.token && request.data?.actionCode !== ACTION_CODE.DANG_NHAP) {
      let eSignature = "";
      // nếu data là kiểu FormData -> upload file
      if (request.data instanceof FormData) {
        const data: {[key: string]: any} = {
          ma_doi_tac_ql: "",
          bt: "",
          ten: "",
          nhom: "",
          actionCode: "",
        };
        for (const [key, value] of request.data.entries()) data[key] = value;

        let formatPayload = "";
        let eSignature = "";

        // Special signature format for Excel import Đoạn này Tuyên thêm
        if (data.actionCode === ACTION_CODE.UPLOAD_EXCEL_FILE) {
          // Format: epartnercode={0}&eauthtoken={1}&time={2}
          const currentDate = new Date().toISOString().slice(0, 10).replace(/-/g, ""); // yyyyMMdd format
          formatPayload = `epartnercode=${env.VITE_E_PARTNER_CODE}&eauthtoken=${profile?.token}&time=${currentDate}`;
          eSignature = await sha256(Base64x.utf8tob64u(formatPayload) + "." + env.VITE_SECRET_KEY);

          // console.log("📝 Excel Import Signature:", {
          //   formatPayload,
          //   currentDate,
          //   ePartnerCode: env.VITE_E_PARTNER_CODE,
          //   eAuthToken: profile?.token?.substring(0, 20) + "...",
          //   eSignature: eSignature.substring(0, 20) + "...",
          //   fullSignature: eSignature
          // });
        } else {
          // Default signature format for other file uploads
          formatPayload = `ma_doi_tac_ql=${data.ma_doi_tac_ql}&bt=${data.bt}&ten=${data.ten}&nhom=${data.nhom}`;
          eSignature = await sha256(Base64x.utf8tob64u(formatPayload) + "." + env.VITE_SECRET_KEY);
        }

        //thêm header mặc định
        request.headers.set("eAction", data.actionCode);
        request.headers.set("eAuthToken", profile?.token);
        request.headers.set("eSignature", eSignature);
        request.headers.set("Content-Type", "multipart/form-data");
      }
      // không thì thực hiện như thường
      else {
        const actionCode = request.data?.actionCode;
        delete request.data.actionCode;
        eSignature = sha256(Base64x.utf8tob64u(JSON.stringify(request.data)) + "." + env.VITE_SECRET_KEY);
        request.headers.set("Content-Type", "application/json");
        //thêm header mặc định
        request.headers.set("eAction", actionCode);
        request.headers.set("eAuthToken", profile?.token);
        request.headers.set("eSignature", eSignature);
      }
    }
    return request;
  },
  (error: {config: any; data: any; headers: any; request: any; status: number; statusText: ""}) => {
    return handleError(error);
  },
);

axiosInstance.interceptors.response.use(
  <T>(response: AxiosResponse<T>) => {
    // For blob responses (like PDF export), return raw response
    if (response.config.responseType === "blob") {
      // console.log("🔄 Blob response detected, returning raw response");
      return response;
    }

    if (response.data !== null && response.data !== undefined) {
      return handleResponse(response.data as any);
    } else {
      return response;
    }
  },
  (error: {config: any; data: any; headers: any; request: any; status: number; statusText: ""}) => {
    return handleError(error);
  },
);

export const updateHeader = (headers: any) => (axiosInstance.defaults.headers = {...axiosInstance.defaults.headers, ...headers});
