{"name": "react", "private": true, "version": "1.0.0", "scripts": {"start": "vite", "build": "yarn clean-cache && tsc --noEmit --skipLibCheck && vite build", "build-no-typecheck": "yarn clean-cache && vite build --mode production", "build-dev": "yarn clean-cache && tsc && vite buil --mode development", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 20", "li": "dot_clean -n . && node generateAssetResource.js", "lint-fix": "eslint . --fix", "preview": "vite preview", "preview-host": "vite preview --host", "test": "jest", "test-watch": "jest --watch", "postinstall": "patch-package", "mac-copy-dev": "pbcopy < .env-template-dev && pbpaste > .env.development", "mac-copy-prod": "pbcopy < .env-template-prod && pbpaste > .env.production", "mac-copy-env": "yarn mac-copy-dev && yarn mac-copy-prod", "clean-cache": "yarn cache clean -f", "clean-node": "rm -rf node_modules && yarn install", "gen": "node scripts/generateSource.js", "gen-page": "node scripts/generatePage.js"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@loadable/component": "^5.15.3", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "antd": "^5.24.6", "antd-style": "^3.7.1", "apexcharts": "^4.5.0", "axios": "^1.4.0", "hjson": "^3.2.2", "i18next": "^22.5.1", "js-cookie": "^3.0.5", "js-sha256": "^0.11.0", "jsrsasign": "^10.8.6", "lodash": "^4.17.21", "moment": "^2.30.1", "patch-package": "^7.0.0", "pdfjs-dist": "^3.0.279", "postinstall-postinstall": "^2.1.0", "rc-virtual-list": "^3.5.2", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-dom": "^18.2.0", "react-highlight-words": "^0.21.0", "react-hook-form": "^7.55.0", "react-i18next": "^12.3.1", "react-number-format": "^5.4.4", "react-query": "^3.39.3", "react-router-dom": "^6.11.1", "sass": "^1.62.1", "scss": "^0.2.4", "styled-components": "^6.1.17", "tailwind-merge": "^1.12.0", "zustand": "^4.3.8"}, "devDependencies": {"@babel/core": "^7.21.8", "@babel/preset-env": "^7.21.5", "@babel/preset-typescript": "^7.21.5", "@jest/globals": "^29.5.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.1", "@types/js-cookie": "^3.0.3", "@types/jsrsasign": "^10.5.15", "@types/loadable__component": "^5.13.4", "@types/lodash": "^4.14.195", "@types/node": "^20.1.1", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/react-highlight-words": "^0.20.0", "@typescript-eslint/eslint-plugin": "^5.59.7", "@typescript-eslint/parser": "^5.59.7", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "babel-jest": "^29.5.0", "eslint": "^8.41.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.3.4", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-unused-imports": "^2.0.0", "jest": "^29.5.0", "jest-css-modules-transform": "^4.4.2", "jest-environment-jsdom": "^29.5.0", "path": "^0.12.7", "postcss": "^8.4.23", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "react-number-format": "^5.4.4", "tailwindcss": "^3.3.2", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.0.4", "vite": "^4.3.2"}}